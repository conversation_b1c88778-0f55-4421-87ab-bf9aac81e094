# 保理核额审查多Agent系统 - 代码架构文档

## 1. 项目概述

本项目是一个基于LangGraph构建的智能保理核额审查系统，采用多Agent协作模式处理Excel文件中的保理业务数据，实现自动化的核额计算和审查。系统采用现代化的人机交互设计，支持智能Agent在需要时主动请求用户澄清信息。

## 2. 技术栈

- **框架**: LangGraph (多Agent工作流编排)
- **LLM集成**: LangChain + Moonshot API (Kimi模型)
- **数据处理**: pandas + openpyxl
- **数据存储**: SQLite (本地持久化)
- **CLI界面**: Typer + Rich (美化输出)
- **人机交互**: LangGraph interrupt机制
- **开发工具**: uv (包管理)

## 3. 项目结构

```
project-hesuan/
├── main.py                    # 项目入口点
├── src/
│   ├── cli.py                # 命令行界面和主工作流控制
│   ├── workflow.py           # LangGraph工作流编排
│   ├── state.py             # 工作流状态定义
│   ├── database.py          # SQLite数据库管理
│   ├── utils.py             # 工具函数
│   └── agents/              # Agent实现目录
│       ├── file_parser.py   # 文件解析分类Agent
│       ├── account_bill.py  # 对账单处理Agent
│       ├── work_statistics.py # 用工统计表处理Agent
│       ├── payroll.py       # 发薪流水处理Agent
│       └── calculation.py   # 核算输出Agent
├── docs/                    # 文档目录
├── testdata/               # 测试数据
└── database/               # 数据库文件存储
```

## 4. 核心架构设计

### 4.1 入口层 (main.py)

```python
#!/usr/bin/env python3
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.cli import app

if __name__ == "__main__":
    app()
```

- **职责**: 项目启动入口，配置Python路径
- **设计**: 简洁的启动器，将控制权交给CLI模块

### 4.2 CLI层 (src/cli.py)

#### 核心功能
- **命令定义**: 使用Typer定义`process`、`demo`、`version`命令
- **工作流控制**: 管理LangGraph工作流的执行和人机交互
- **输出格式化**: 支持table、json、report三种输出格式
- **人机交互处理**: 基于LangGraph标准interrupt机制的用户交互

#### 关键设计模式

**1. 标准化人机交互循环**
```python
while True:
    for event in workflow.app.stream(inputs, config=config, stream_mode="values"):
        final_result = event

    # 检查是否需要人工干预
    if messages and isinstance(messages[-1], AIMessage) and \
       messages[-1].tool_calls and \
       messages[-1].tool_calls[0]['name'] == 'ask_user_for_clarification':
        # 获取用户输入
        user_response = Prompt.ask("[bold]您的回答[/bold]")
        # 使用LangGraph标准Command(resume=...)方式恢复执行
        stream = workflow.app.stream(Command(resume=user_response), config=config, stream_mode="values")
    else:
        break  # 工作流正常结束
```

**2. 会话管理**
- 使用文件路径生成确定性的`thread_id`
- 支持工作流状态的持久化和恢复
- 基于LangGraph checkpointer机制

### 4.3 工作流层 (src/workflow.py)

#### LangGraph图结构
```python
# 核心节点定义
workflow.add_node("file_parser", self.file_parser)
workflow.add_node("account_bill", self.account_bill_processor)
workflow.add_node("work_statistics_agent", work_statistics_agent_node)
workflow.add_node("work_statistics_tools", self.execute_work_statistics_tools)
workflow.add_node("payroll", self.payroll_processor)
workflow.add_node("calculation", self.calculation_agent)

# 简化的路由逻辑
workflow.add_conditional_edges("file_parser", self._route_after_parsing)
workflow.add_conditional_edges("work_statistics_agent", decide_next_step)
workflow.add_conditional_edges("work_statistics_tools", self.route_after_work_statistics_tools)
```

#### 执行流程
1. **START** → **file_parser** (文件解析和分类)
2. **路由判断** → 根据文件类型选择处理路径
3. **数据处理** → account_bill 或 work_statistics_agent 处理
4. **工具执行** → work_statistics_tools (如果是用工统计)
5. **发薪处理** → payroll 处理
6. **最终计算** → calculation 输出结果

#### 编译配置
```python
self.app = self.workflow.compile(
    checkpointer=self.checkpointer  # 仅需要checkpointer支持interrupt
)
```

### 4.4 状态管理 (src/state.py)

#### WorkflowState设计
```python
class WorkflowState(TypedDict):
    # 核心输入
    file_path: str
    project_id: int
    
    # 文件解析结果
    sheet_classifications: Dict[str, str]
    calculation_mode: str
    project_name: str
    
    # Agent处理结果
    settlement_data: List[Dict]
    payroll_data: List[Dict]
    
    # 交互式Agent支持
    messages: Annotated[List, add_messages]
    sheet_name: Optional[str]
    
    # 最终结果
    final_result: Dict[str, Any]
```

**设计特点**:
- **类型安全**: 使用TypedDict确保状态字段类型
- **消息累积**: 使用`add_messages`支持对话历史
- **错误处理**: 内置错误收集机制

## 5. Agent架构设计

### 5.1 文件解析分类Agent (file_parser.py)

**职责**: 系统入口，负责Excel文件解析和Sheet分类

**核心流程**:
1. **文件读取**: 智能读取所有Sheet的样本数据
2. **LLM分析**: 调用Kimi模型进行Sheet类型分类
3. **模式判断**: 确定核额计算模式
4. **数据库初始化**: 创建项目记录

**输出结果**:
```python
class FileAnalysisResult(BaseModel):
    sheets: List[SheetClassification]
    calculation_mode: str  # "账单模式-先发后融" 等
    project_name: str
```

### 5.2 用工统计表处理Agent (work_statistics.py)

**架构特点**: 采用"模式驱动"设计，基于LangGraph标准interrupt机制实现人机交互

#### 核心组件

**1. Agent节点** (`work_statistics_agent_node`)
- **职责**: 分析决策，选择合适的执行工具
- **工具集**: 分析工具 + 执行工具 + 人机交互工具

**2. 工具节点** (`execute_work_statistics_tools`)
- **职责**: 执行具体的数据处理任务
- **实现**: 自定义工具执行节点，支持状态更新

**3. 决策路由** (`decide_next_step`)
- **职责**: 智能路由，正确处理不同消息类型
- **实现**: 区分AIMessage和ToolMessage，确保流程连续性

#### 工具设计

**分析工具**:
- `get_sheet_preview`: 获取表格结构预览
- `ask_user_for_clarification`: 基于interrupt()的用户交互工具

**执行工具** (原子操作):
- `process_with_multiplication`: 处理"数量×单价"场景
- `process_with_fixed_rate`: 处理"数量×固定单价"场景
- `process_from_direct_amount`: 处理直接金额提取场景

#### 人机交互机制

**interrupt()函数实现**:
```python
@tool
def ask_user_for_clarification(question: str) -> Tuple[str, str]:
    # 使用interrupt函数暂停执行，等待用户输入
    user_response = interrupt(question)
    return "success", f"用户回答: {user_response}"
```

**决策路由实现**:
```python
def decide_next_step(state: WorkflowState) -> str:
    last_message = state['messages'][-1]

    # 检查消息类型 - 只有AIMessage才有tool_calls属性
    if not isinstance(last_message, AIMessage):
        # 如果是ToolMessage（用户回答），返回到agent继续处理
        return "work_statistics_agent"

    # 处理AIMessage的工具调用逻辑
    if not last_message.tool_calls:
        return END

    # 所有工具调用都统一路由到工具节点
    return "call_tool"
```

#### 工作流程示例
```
1. Agent调用get_sheet_preview分析表格
2. 发现缺少单价信息
3. Agent调用ask_user_for_clarification询问用户
4. 工具内部调用interrupt()暂停执行
5. CLI层检测到中断，获取用户输入
6. 使用Command(resume=user_response)恢复执行
7. Agent根据用户回答选择process_with_fixed_rate执行
8. 工具完成数据处理并返回结果
```

### 5.3 其他Agent

**对账单处理Agent** (`account_bill.py`): 处理对账单数据的解析和计算
**发薪流水处理Agent** (`payroll.py`): 处理发薪数据和人员匹配
**核算输出Agent** (`calculation.py`): 执行最终的核额计算和报告生成

## 6. 数据流设计

### 6.1 数据存储策略

**SQLite本地数据库**:
- **项目表**: 存储项目基本信息
- **结算数据表**: 存储对账单/用工统计表数据
- **发薪数据表**: 存储发薪流水数据

**优势**:
- 轻量级，无需复杂配置
- 支持多Agent间数据共享
- 本地持久化，支持断点续传

### 6.2 数据传递机制

**状态传递**: 通过WorkflowState在Agent间传递元数据
**数据库共享**: 大量表格数据通过数据库在Agent间共享
**消息历史**: 支持交互式Agent的对话上下文

## 7. 人机交互设计

### 7.1 基于LangGraph标准interrupt机制

**核心原理**: 使用LangGraph内置的`interrupt()`函数实现标准化人机交互
**触发方式**: Agent调用`ask_user_for_clarification`工具时自动触发
**恢复机制**: 使用`Command(resume=...)`标准方式恢复执行

### 7.2 交互流程

```python
# 1. Agent调用工具
@tool
def ask_user_for_clarification(question: str):
    # 工具内部使用interrupt()暂停执行
    user_response = interrupt(question)
    return "success", f"用户回答: {user_response}"

# 2. CLI层检测中断并处理
if tool_call['name'] == 'ask_user_for_clarification':
    user_response = Prompt.ask("[bold]您的回答[/bold]")
    # 使用标准Command(resume=...)恢复
    stream = workflow.app.stream(Command(resume=user_response), config=config)

# 3. 工作流自动恢复并继续执行
```

### 7.3 设计优势

- **标准化**: 遵循LangGraph最佳实践
- **简洁性**: 无需复杂的中断配置
- **可靠性**: 避免手动状态管理的错误
- **可扩展性**: 易于添加新的交互点

## 8. 错误处理和日志

### 8.1 错误处理策略

**分层错误处理**:
- **Agent层**: 捕获业务逻辑错误
- **工作流层**: 处理流程控制错误  
- **CLI层**: 统一错误输出和用户提示

### 8.2 日志系统

**Rich美化输出**: 使用Rich库提供彩色、结构化的终端输出
**分级日志**: 支持info、success、error等不同级别
**详细模式**: 通过`--verbose`参数控制日志详细程度

## 9. 扩展性设计

### 9.1 Agent扩展

**标准接口**: 所有Agent实现统一的`__call__(state) -> state`接口
**工具化设计**: 复杂Agent可拆分为多个工具组合
**状态兼容**: 新Agent可复用现有状态字段

### 9.2 模式扩展

**计算模式**: 通过配置支持新的核额计算规则
**文件格式**: 可扩展支持更多Excel格式和结构
**输出格式**: 支持新的结果输出格式

## 10. 性能优化

### 10.1 数据处理优化

**大数据处理**: 表格数据在代码层流动，避免LLM上下文传递
**内存管理**: 使用pandas进行高效的数据操作
**缓存机制**: 数据库提供天然的数据缓存

### 10.2 LLM调用优化

**精简上下文**: 只传递必要的分析信息给LLM
**工具原子化**: 减少LLM决策复杂度
**模型选择**: 使用成本效益最优的Kimi模型

## 11. 部署和使用

### 11.1 环境配置

```bash
# 安装依赖
uv sync

# 环境变量配置
MOONSHOT_API_KEY=your_api_key
MODEL_NAME=moonshot-v1-8k
TEMPERATURE=0.3
```

### 11.2 使用方式

```bash
# 基本使用
python main.py process data.xlsx

# 详细模式
python main.py process data.xlsx --verbose

# 不同输出格式
python main.py process data.xlsx --format json
python main.py process data.xlsx --format report --save

# 演示模式
python main.py demo
```

## 12. 总结

本系统采用现代化的多Agent架构，基于LangGraph最佳实践实现了智能化的工作流编排，具有以下特点：

- **标准化设计**: 遵循LangGraph最佳实践，使用标准interrupt机制
- **智能交互**: 基于interrupt()函数的无缝人机协作
- **简洁架构**: 移除复杂的中断配置，采用Command(resume=...)标准恢复
- **类型安全**: 正确处理AIMessage和ToolMessage类型
- **数据驱动**: 基于SQLite的数据共享机制，支持复杂的数据处理流程
- **用户友好**: Rich美化的CLI界面，提供良好的用户体验
- **可维护性**: 清晰的职责分离和标准化的接口设计

该架构既保证了系统的稳定性和可靠性，又提供了足够的灵活性来适应不同的业务需求，同时符合LangGraph的设计理念和最佳实践。

